<?php

declare(strict_types=1);

namespace App\Utils;

use App\Model\CacheStorageService;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasStaticCache;
use DateInterval;
use DateTimeImmutable;
use DateTimeZone;
use Exception;
use Nette;
use ReflectionException;
use Ya<PERSON><PERSON>\ProviderInterface;
use <PERSON><PERSON><PERSON>\Yasumi;
use function date;

class DateTime extends Nette\Utils\DateTime
{
	use HasStaticCache;

	public const FORMAT_DEFAULT = 'j.n.Y H:i:s';
	public const FORMAT_DATE = 'j.n.Y';
	public const FORMAT_DATE_SPACE = 'j. n. Y';

	/** average year in days */
	public const YEAR_IN_DAYS = Nette\Utils\DateTime::YEAR / 60 / 60 / 24;

	private ProviderInterface $holidays;
	protected static CacheStorageService $cacheStorageService;

	public function __construct(mixed $datetime = 'now', ?DateTimeZone $timezone = null, ?State $state = null)
	{
		parent::__construct($datetime, $timezone);
		$this->setHolidays($state);
	}


	/**
	 * @throws ReflectionException
	 */
	public function setHolidays(?State $state = null): DateTime
	{
		$isoCode = $state ? $state->code : State::DEFAULT_CODE;
		bdump($isoCode);
		$generator = function () use ($isoCode) {
			$storageGenerator = function () use ($isoCode) {
				return Yasumi::createByISO3166_2($isoCode, (int) $this->format('Y'));
			};
			return $storageGenerator();
			//return self::$cacheStorageService->getStoredData('stateHoliday-' . (int) $this->format('Y'), $isoCode, $storageGenerator);
		};
		$this->holidays = $this->loadCache('stateHoliday-' . $isoCode, $generator);
		bdump($this->holidays);
		return $this;
	}

	public static function setCacheStorageService(CacheStorageService $cacheStorageService): void
	{
		static::$cacheStorageService = $cacheStorageService;
	}


	public function addWorkday(int $amount = 1): DateTime
	{
		for ($i = 0; $i < $amount; $i++) {
			$this->modify('+1 day');
			while (!$this->isWorkingDay()) {
				$this->modify('+1 day');
			}
		}

		return $this;
	}


	/**
	 * Vrátí nejbližší pracovní den (včetně aktuálního)
	 */
	public function getClosestWorkday(): DateTime
	{
		while (!$this->isWorkingDay()) {
			$this->modify('+1 day');
		}

		return $this;
	}


	/**
	 * Vrátí defaultně počet dní od teď
	 *
	 * !! př. 18.1.2016 16:00 -> 20.1.2016 14:00 => 1 den a 22 hodin
	 */
	public function getDistanceFromNow(string $type = 'd'): DateInterval|int
	{
		$today = new self;
		$diff = $this->diff($today);

		if ($type) {
			return $diff->{$type};
		}

		return $diff;
	}


	/**
	 * Vrátí abs počet dní od teď vyjádřeno na dny
	 *
	 * !! př. 18.1.2016 16:00 -> 20.1.2016 14:00 => 2
	 *
	 * @return int
	 * @throws Exception
	 */
	public function getDaysFromNow(): int
	{
		$today = new self(date('Y-m-d'));
		$diff = $this->diff($today)->days;
		if ($diff === false) {
			throw new Exception();
		}

		return $diff;
	}

	public function getYearsFromNow(): float
	{
		return $this->getDaysFromNow() / self::YEAR_IN_DAYS;
	}

	public function isToday(): bool
	{
		$now = new DateTimeImmutable();
		return $this->format('Y-m-d') === self::from($now)->format('Y-m-d');
	}


	public function isTommorow(): bool
	{
		return $this->getDaysFromNow() === 1;
	}


	public function isWorkingDay(): bool
	{
		return $this->holidays->isWorkingDay($this);
	}


	public function isTomorrowWorkingDay(): bool
	{
		$day = clone $this;
		$day->modify('+1 day');
		return $day->isWorkingDay();
	}


	public function isFuture(DateTimeImmutable $now): bool
	{
		return $this > $now;
	}


	public function isPast(DateTimeImmutable $now): bool
	{
		return !$this->isFuture($now);
	}
}
